"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import { PanelRight } from "lucide-react"

export function SidebarToggleButton() {
  const { isCollapsed, expand } = useSidebarStore()

  // Only show when sidebar is collapsed
  if (!isCollapsed) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            onClick={expand}
            className="fixed left-[90px] bottom-[18px] z-10 flex h-10 w-10 items-center justify-center rounded-sm cursor-pointer text-muted-foreground hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors p-2"
          >
            <PanelRight className="h-6 w-6" />
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="right"
          className="bg-black text-white dark:bg-black dark:text-white border-none shadow-lg"
        >
          Open Toolbar
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
